{"moduleFileExtensions": ["js", "json", "ts"], "rootDir": ".", "testEnvironment": "node", "coverageDirectory": "../tests/coverage", "testRegex": ".spec.ts$", "moduleNameMapper": {"@src/(.*)$": "<rootDir>/src/$1", "@modules/(.*)$": "<rootDir>/src/modules/$1", "@config/(.*)$": "<rootDir>/src/configs/$1", "@libs/(.*)$": "<rootDir>/src/libs/$1", "@exceptions$": "<rootDir>/src/libs/exceptions", "@tests/(.*)$": "<rootDir>/tests/$1"}, "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "setupFiles": ["<rootDir>/tests/unit/setup.ts"]}