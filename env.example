PORT=3100
#MYSQL_HOST=127.0.0.1
# For Docker
MYSQL_HOST=host.docker.internal
MYSQL_USERNAME=root
MYSQL_PASSWORD=123456
MYSQL_DATABASE=financial
MYSQL_PORT=3306
MYSQL_POOL=
DB_SYNCHRONIZE=false
DB_LOGGING=false

APISIX_SOURCE_KEY=uHE7taxrNNrDuwh3AVMK8Hd3myPQdK6E
SERVICE_API_KEY=your_service_api_key
APISIX_URL=https://apisix.onead.com.tw
MASA_URL=http://host.docker.internal:4001
ERP_URL=http://host.docker.internal:4000
