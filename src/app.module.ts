import { Module } from '@nestjs/common'
import { APP_GUARD } from '@nestjs/core'
import { CqrsModule } from '@nestjs/cqrs'
import { EventEmitterModule } from '@nestjs/event-emitter'
import { TypeOrmModule } from '@nestjs/typeorm'
import { RequestContextModule } from 'nestjs-request-context'
import { databaseConfig } from './config/databaseConfig'
import { AuthModule } from './libs/externalServices/auth/authModule'
import { AuthGuard } from './libs/guards/authGuard'
import { AccountingModule } from './modules/accounting/accountingModule'
import { TypeOrmMainAccountEntity } from './modules/accounting/database/typeorm/typeOrmMainAccountEntity'
import { TypeOrmSubAccountEntity } from './modules/accounting/database/typeorm/typeOrmSubAccountEntity'
import { ApplicationFormModule } from './modules/applicationForm/applicationFormModule'
import { TypeOrmApplicationFormEntity } from './modules/applicationForm/database/typeorm/typeOrmApplicationFormEntity'
import { CounterpartyModule } from './modules/counterparty/counterpartyModule'
import { TypeOrmCounterpartyEntity } from './modules/counterparty/database/typeorm/typeOrmCounterpartyEntity'
import { TypeOrmFinancialRecordEntity } from './modules/financialRecord/database/typeorm/typeOrmFinancialRecordEntity'
import { TypeOrmFinancialRecordLogEntity } from './modules/financialRecord/database/typeorm/typeOrmFinancialRecordLogEntity'
import { FinancialRecordModule } from './modules/financialRecord/financialRecordModule'
import { TypeOrmSubsidiaryEntity } from './modules/subsidiary/database/typeorm/typeOrmSubsidiaryEntity'
import { SubsidiaryModule } from './modules/subsidiary/subsidiaryModule'

@Module({
  imports: [
    TypeOrmModule.forRoot({
      type: 'mysql',
      host: databaseConfig.host,
      port: databaseConfig.port,
      username: databaseConfig.username,
      password: databaseConfig.password,
      database: databaseConfig.database,
      entities: [
        TypeOrmFinancialRecordEntity,
        TypeOrmFinancialRecordLogEntity,
        TypeOrmSubsidiaryEntity,
        TypeOrmApplicationFormEntity,
        TypeOrmCounterpartyEntity,
        TypeOrmMainAccountEntity,
        TypeOrmSubAccountEntity,
      ],
      synchronize: false,
    }),
    AuthModule,
    CqrsModule,
    EventEmitterModule.forRoot(),
    RequestContextModule,
    FinancialRecordModule,
    SubsidiaryModule,
    ApplicationFormModule,
    CounterpartyModule,
    AccountingModule,
  ],
  controllers: [],
  providers: [
    {
      provide: APP_GUARD,
      useExisting: AuthGuard,
    },
    AuthGuard,
  ],
})
export class AppModule {}
