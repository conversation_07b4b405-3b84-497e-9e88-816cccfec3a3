import { get } from 'env-var'
import 'dotenv/config'

export interface DatabaseConfig {
  host: string
  port: number
  username: string
  password: string
  database: string
}

export const databaseConfig: DatabaseConfig = {
  host: get('MYSQL_HOST').default('localhost').asString(),
  port: get('MYSQL_PORT').default('3306').asIntPositive(),
  username: get('MYSQL_USERNAME').required().asString(),
  password: get('MYSQL_PASSWORD').required().asString(),
  database: get('MYSQL_DATABASE').required().asString(),
}
