import { ApiProperty } from '@nestjs/swagger'
import { Paginated } from '../ddd'

export abstract class PaginatedResponseDto<T> extends Paginated<T> {
  @ApiProperty({
    example: 5312,
    description: 'Total number of items',
  })
  declare readonly totalCounts: number

  @ApiProperty({
    example: 5312,
    description: 'Total number of pages',
  })
  declare readonly pageCounts: number

  @ApiProperty({ example: 0, description: 'Page number' })
  declare readonly currentPage: number

  @ApiProperty({
    example: 10,
    description: 'Number of items per page',
  })
  declare readonly itemCounts: number

  @ApiProperty({ isArray: true })
  declare abstract readonly data: readonly T[]
}
