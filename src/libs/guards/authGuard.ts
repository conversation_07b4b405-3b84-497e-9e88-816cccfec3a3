import {
  CanActivate,
  ExecutionContext,
  Inject,
  Injectable,
  UnauthorizedException,
} from '@nestjs/common'
import { get } from 'env-var'
import { IAuthService } from '../externalServices/auth'
import 'dotenv/config'

export interface RequestWithUser extends Request {
  userId?: string
}
@Injectable()
export class AuthGuard implements CanActivate {
  constructor(
    @Inject('IAuthService') private readonly authService: IAuthService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    console.log('AuthGuard canActivate called')
    const request = context.switchToHttp().getRequest<RequestWithUser>()
    const authHeader = request.headers['authorization'] as string
    const serviceApiKey = request.headers['service-api-key'] as string
    const serviceApiKeyFromEnv = get('SERVICE_API_KEY').required().asString()

    if (!authHeader && !serviceApiKey) {
      throw new UnauthorizedException(
        'Either accessToken or Service-API-Key must be provided.',
      )
    }

    if (authHeader && serviceApiKey) {
      throw new UnauthorizedException(
        'Both accessToken and Service-API-Key provided, which is not allowed.',
      )
    }

    if (serviceApiKey && serviceApiKey !== serviceApiKeyFromEnv) {
      throw new UnauthorizedException('Unauthorized Service-API-Key')
    }

    if (authHeader) {
      const token = authHeader.split(' ')[1]
      const validationResult = await this.authService.validateUserToken(token)

      if (!validationResult.isValid) {
        throw new UnauthorizedException(
          validationResult.message || 'Unauthorized User',
        )
      }
      request.userId = validationResult.userId?.toString()
    }

    return true
  }
}
