/** Consider creating a bunch of shared custom utility
 * types for different situations.
 * Alternatively you can use a library like
 * https://github.com/andnp/SimplyTyped
 */
export * from './deep-partial.type'
export * from './non-function-properties.type'
export * from './object-literal.type'
export * from './require-one.type'
export * from './mutable.type'
export * from './optionalType'
export * from './nullableType'
