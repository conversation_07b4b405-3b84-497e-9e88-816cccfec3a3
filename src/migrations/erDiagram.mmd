erDiagram
    MainAccount {
        int id PK
        string name
        boolean is_enable
        datetime created_at
        datetime updated_at
    }

    SubAccount {
        int id PK
        string name
        boolean is_enable
        int main_account_id FK
        boolean is_debit
        int application_form_id FK
        datetime created_at
        datetime updated_at
    }

    Subsidiary {
        int id PK
        string name
        boolean is_enable
    }

    Counterparty {
        int id PK
        string source_table
        string source_id
        enum type "DOMESTIC_COMPANY, DOMESTIC_PERSON, FOREIGN_COMPANY, FOREIGN_PERSON"
        string name
        string identity_number
        text address
        boolean is_enable
        datetime created_at
        datetime updated_at
    }

    ApplicationForms {
        int id PK
        string name
        boolean is_enable
    }

    FinancialRecord {
        int id PK
        int subsidiary_id FK
        enum transaction_type "INCOME, EXPENSE"
        int counterparty_id FK
        int sub_account_id FK
        date date
        string currency_code
        decimal exchange_rate
        decimal adjusted_exchange_rate
        decimal amount
        decimal adjusted_amount
        decimal TWD_amount
        decimal adjusted_TWD_amount
        string accrual_voucher_number
        string actual_voucher_number
        string invoice_number
        string uniform_invoice_number
        date invoice_date
        string note
        boolean is_locked
        boolean is_deleted
        int creator_id
        datetime created_at
        datetime updated_at
    }

    FinancialRecordsLog {
        int id PK
        int financial_record_id FK
        int user_id
        json old_values
        json new_values
        text change_reason
        datetime created_at
    }

    MainAccount ||--o{ SubAccount : has
    ApplicationForms ||--o{ SubAccount : includes
    SubAccount ||--o{ FinancialRecord : records
    Subsidiary ||--o{ FinancialRecord : owns
    Counterparty ||--o{ FinancialRecord : involves
    FinancialRecord ||--o{ FinancialRecordsLog : logs
