import { MigrationInterface, QueryRunner, Table } from 'typeorm'

export class CreateFinancialRecordsLogTable1740453701911
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    const exists = await queryRunner.hasTable('financial_records_log')
    if (!exists) {
      await queryRunner.createTable(
        new Table({
          name: 'financial_records_log',
          columns: [
            {
              name: 'id',
              type: 'int',
              isPrimary: true,
              isGenerated: true,
              generationStrategy: 'increment',
            },
            {
              name: 'financial_record_id',
              type: 'int',
              isNullable: false,
            },
            {
              name: 'user_id',
              type: 'int',
              isNullable: false,
            },
            {
              name: 'old_values',
              type: 'json',
              isNullable: true,
            },
            {
              name: 'new_values',
              type: 'json',
              isNullable: true,
            },
            {
              name: 'change_reason',
              type: 'text',
              isNullable: false,
            },
            {
              name: 'created_at',
              type: 'datetime',
              default: 'CURRENT_TIMESTAMP',
            },
          ],
        }),
      )
    }
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    const exists = await queryRunner.hasTable('financial_records_log')
    if (exists) {
      await queryRunner.dropTable('financial_records_log')
    }
  }
}
