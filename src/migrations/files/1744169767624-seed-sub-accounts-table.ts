import { MigrationInterface, QueryRunner } from 'typeorm'

export class SeedSubAccountsTable1744169767624
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      INSERT INTO sub_accounts (name, main_account_id, application_form_id, is_debit)
      VALUES
        ('OneAD 平台收入', 1, 1, true),
        ('數據交易市集收入', 1, 2, true),
        ('Taboola 流量開發 服務收入', 1, 4, true),
        ('Taboola 流量開發 服務成本', 2, 4, true),
        ('SuperWall 服務收入', 1, 3, true),
        ('SuperWall 服務成本', 2, 3, true),
        ('雲計算成本', 2, 7, true)
    `)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      DELETE FROM sub_accounts WHERE name IN (
        'OneAD 平台收入',
        '數據交易市集收入',
        'Taboola 流量開發 服務收入',
        'Taboola 流量開發 服務成本',
        'SuperWall 服務收入',
        'SuperWall 服務成本',
        '雲計算成本'
      )
    `)
  }
}
