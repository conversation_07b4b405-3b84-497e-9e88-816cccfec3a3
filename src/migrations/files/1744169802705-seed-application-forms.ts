import { MigrationInterface, QueryRunner } from 'typeorm'

export class SeedApplicationForms1744169802705 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      INSERT INTO application_forms (name) VALUES
      ('OneAD 平台'),
      ('數據交易市集'),
      ('SuperWall 服務'),
      ('Taboola 流量開發 服務'),
      ('網路流量代管 服務'),
      ('CDN'),
      ('雲計算成本'),
      ('第三方M&M')
    `)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      DELETE FROM application_forms WHERE name IN (
        'OneAD 平台',
        '數據交易市集',
        'SuperWall 服務',
        'Taboola 流量開發 服務',
        '網路流量代管 服務',
        'CDN',
        '雲計算成本',
        '第三方M&M'
      )
    `)
  }
}
