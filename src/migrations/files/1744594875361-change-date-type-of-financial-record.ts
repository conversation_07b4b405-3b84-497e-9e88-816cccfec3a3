import { MigrationInterface, QueryRunner } from 'typeorm'

export class ChangeDateTypeOfFinancialRecord1744594875361
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE financial_records MODIFY COLUMN invoice_date DATE`,
    )
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE financial_records MODIFY COLUMN invoice_date DATETIME`,
    )
  }
}
