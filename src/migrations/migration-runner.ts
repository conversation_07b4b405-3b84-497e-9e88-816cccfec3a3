import 'reflect-metadata'
import { DataSource } from 'typeorm'
import { databaseConfig } from '../config/databaseConfig'

// 建立更強健的 DataSource 設定
const createDataSource = () =>
  new DataSource({
    type: 'mysql',
    host: databaseConfig.host,
    port: databaseConfig.port,
    username: databaseConfig.username,
    password: databaseConfig.password,
    database: databaseConfig.database,
    synchronize: false,
    logging: true, // 開啟日誌以便除錯
    entities: [],
    migrations: ['src/migrations/files/*.ts'],
    migrationsTableName: 'migrations',
    // 連線設定
    connectTimeout: 5000, // 5 秒連線超時
    extra: {
      connectionLimit: 1, // 單一連線
      ssl: false,
      // 增加相容性設定
      authPlugins: {
        mysql_native_password: () => () => Buffer.alloc(0),
      },
    },
  })

// 重試機制
const retryOperation = async <T>(
  operation: () => Promise<T>,
  maxRetries: number = 3,
  delay: number = 2000,
): Promise<T> => {
  for (let i = 0; i < maxRetries; i++) {
    try {
      return await operation()
    } catch (error) {
      console.log(
        `❌ 嘗試 ${i + 1}/${maxRetries} 失敗:`,
        (error as Error).message,
      )

      if (i === maxRetries - 1) {
        throw error
      }

      console.log(`⏳ ${delay / 1000} 秒後重試...`)
      await new Promise((resolve) => setTimeout(resolve, delay))
    }
  }
  throw new Error('重試次數已用盡')
}

// 執行 migration
export const runMigrations = async (): Promise<void> => {
  const dataSource = createDataSource()

  try {
    console.log('⏳ 正在連線到資料庫...')

    await retryOperation(async () => {
      await dataSource.initialize()
      console.log('✅ 資料庫連線成功')
    })

    console.log('⚙️ 正在執行 migration...')
    const results = await dataSource.runMigrations()

    if (results.length === 0) {
      console.log('ℹ️ 沒有待執行的 migration')
    } else {
      results.forEach((migration) => {
        console.log(`✅ 已執行 migration: ${migration.name}`)
      })
      console.log(`🎉 成功執行 ${results.length} 個 migration!`)
    }
  } catch (error) {
    console.error('❌ Migration 執行失敗:', (error as Error).message)
    process.exit(1)
  } finally {
    if (dataSource.isInitialized) {
      await dataSource.destroy()
      console.log('🔌 資料庫連線已關閉')
    }
  }
}

// 回滾 migration
export const revertMigration = async (): Promise<void> => {
  const dataSource = createDataSource()

  try {
    console.log('⏳ 正在連線到資料庫...')

    await retryOperation(async () => {
      await dataSource.initialize()
      console.log('✅ 資料庫連線成功')
    })

    console.log('⚙️ 正在回滾最後一個 migration...')
    await dataSource.undoLastMigration()
    console.log('✅ Migration 回滾成功!')
  } catch (error) {
    console.error('❌ Migration 回滾失敗:', (error as Error).message)
    process.exit(1)
  } finally {
    if (dataSource.isInitialized) {
      await dataSource.destroy()
      console.log('🔌 資料庫連線已關閉')
    }
  }
}

// 顯示 migration 狀態
export const showMigrations = async (): Promise<void> => {
  const dataSource = createDataSource()

  try {
    console.log('⏳ 正在連線到資料庫...')

    await retryOperation(async () => {
      await dataSource.initialize()
      console.log('✅ 資料庫連線成功')
    })

    console.log('📋 Migration 狀態:')
    const migrations = await dataSource.showMigrations()
    console.log(`ℹ️ 待執行的 migration 數量: ${migrations}`)
  } catch (error) {
    console.error('❌ 無法顯示 migration 狀態:', (error as Error).message)
    process.exit(1)
  } finally {
    if (dataSource.isInitialized) {
      await dataSource.destroy()
      console.log('🔌 資料庫連線已關閉')
    }
  }
}

// 命令列介面
const command = process.argv[2]

switch (command) {
  case 'run':
    runMigrations()
    break
  case 'revert':
    revertMigration()
    break
  case 'show':
    showMigrations()
    break
  default:
    console.log('使用方式:')
    console.log('  ts-node src/migration-runner.ts run    # 執行 migration')
    console.log('  ts-node src/migration-runner.ts revert # 回滾 migration')
    console.log('  ts-node src/migration-runner.ts show   # 顯示狀態')
    process.exit(1)
}
