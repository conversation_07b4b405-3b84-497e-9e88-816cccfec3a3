import { TransactionType } from '@libs/enums/transactionTypeEnums'
import { Nullable } from '@libs/types'
import { Injectable } from '@nestjs/common'
import { InjectRepository } from '@nestjs/typeorm'
import { DataSource, Repository } from 'typeorm'
import { FinancialRecordDetailResponseDto } from '../dtos/financialRecordDetailResponseDto'
import { TypeOrmFinancialRecordEntity } from './typeorm/typeOrmFinancialRecordEntity'

interface FinancialRecordRawData {
  id: number
  subsidiaryId: number
  subsidiaryName: string
  transactionType: string
  counterpartyId: number | null
  counterpartyName: string | null
  identificationNumber: string | null
  counterpartyEntityType: string | null
  registeredAddress: string | null
  subAccountId: number
  subAccountName: string
  mainAccountId: number
  mainAccountName: string
  applicationFormId: number
  applicationFormName: string | null
  date: string
  currencyCode: string
  exchangeRate: string
  adjustedExchangeRate: string
  amount: string
  adjustedAmount: string
  twdAmount: string
  adjustedTwdAmount: string
  accrualVoucherNumber: string | null
  actualVoucherNumber: string | null
  invoiceNumber: string | null
  uniformInvoiceNumber: string | null
  invoiceDate: string | null
  note: string | null
  isLocked: number
  isDeleted: number
  creatorId: number
  creatorName: string
}

@Injectable()
export class TypeOrmFinancialRecordRepositoryQueryAdapter {
  private repository: Repository<TypeOrmFinancialRecordEntity>

  constructor(
    @InjectRepository(TypeOrmFinancialRecordEntity)
    repository: Repository<TypeOrmFinancialRecordEntity>,
    private readonly dataSource: DataSource,
  ) {
    this.repository = repository
  }

  public async findOneById(
    id: string,
  ): Promise<Nullable<TypeOrmFinancialRecordEntity>> {
    const entity = await this.repository.findOne({
      where: { id: Number(id) },
    })
    return entity ? entity : null
  }

  public async findOneByIdWithDetails(
    id: string,
  ): Promise<Nullable<FinancialRecordDetailResponseDto>> {
    const queryBuilder = this.dataSource
      .createQueryBuilder()
      .select('fr.id', 'id')
      .addSelect('subsidiary.id', 'subsidiaryId')
      .addSelect('subsidiary.name', 'subsidiaryName')
      .addSelect('fr.transaction_type', 'transactionType')
      .addSelect('fr.counterparty_id', 'counterpartyId')
      .addSelect('counterparty.name', 'counterpartyName')
      .addSelect('counterparty.identity_number', 'identificationNumber')
      .addSelect('counterparty.type', 'counterpartyEntityType')
      .addSelect('counterparty.address', 'registeredAddress')
      .addSelect('fr.sub_account_id', 'subAccountId')
      .addSelect('sub_account.name', 'subAccountName')
      .addSelect('main_account.id', 'mainAccountId')
      .addSelect('main_account.name', 'mainAccountName')
      .addSelect('sub_account.application_form_id', 'applicationFormId')
      .addSelect('application_form.name', 'applicationFormName')
      .addSelect('fr.date', 'date')
      .addSelect('fr.currency_code', 'currencyCode')
      .addSelect('fr.exchange_rate', 'exchangeRate')
      .addSelect('fr.adjusted_exchange_rate', 'adjustedExchangeRate')
      .addSelect('fr.amount', 'amount')
      .addSelect('fr.adjusted_amount', 'adjustedAmount')
      .addSelect('fr.TWD_amount', 'twdAmount')
      .addSelect('fr.adjusted_TWD_amount', 'adjustedTwdAmount')
      .addSelect('fr.accrual_voucher_number', 'accrualVoucherNumber')
      .addSelect('fr.actual_voucher_number', 'actualVoucherNumber')
      .addSelect('fr.invoice_number', 'invoiceNumber')
      .addSelect('fr.uniform_invoice_number', 'uniformInvoiceNumber')
      .addSelect('fr.invoice_date', 'invoiceDate')
      .addSelect('fr.note', 'note')
      .addSelect('fr.is_locked', 'isLocked')
      .addSelect('fr.is_deleted', 'isDeleted')
      .addSelect('fr.creator_id', 'creatorId')
      .addSelect(
        "CONCAT(creator.firstname, ' ', creator.lastname)",
        'creatorName',
      )
      .from('financial_records', 'fr')
      .leftJoin(
        'sub_accounts',
        'sub_account',
        'sub_account.id = fr.sub_account_id',
      )
      .leftJoin(
        'main_accounts',
        'main_account',
        'main_account.id = sub_account.main_account_id',
      )
      .leftJoin(
        'subsidiaries',
        'subsidiary',
        'subsidiary.id = fr.subsidiary_id',
      )
      .leftJoin(
        'counterparties',
        'counterparty',
        'counterparty.id = fr.counterparty_id',
      )
      .leftJoin(
        'application_forms',
        'application_form',
        'application_form.id = sub_account.application_form_id',
      )
      .leftJoin(
        (qb) => qb.select('*').from('gspadmin.users', 'u'),
        'creator',
        'creator.id = fr.creator_id',
      )
      .where('fr.id = :id', { id: Number(id) })
      .andWhere('fr.is_deleted = :is_deleted', { is_deleted: false })

    const result = await queryBuilder.getRawOne<FinancialRecordRawData>()

    if (!result) {
      return null
    }

    return new FinancialRecordDetailResponseDto({
      ...result,
      transactionType: result.transactionType as TransactionType,
      exchangeRate: parseFloat(result.exchangeRate),
      adjustedExchangeRate: parseFloat(result.adjustedExchangeRate),
      amount: parseFloat(result.amount),
      adjustedAmount: parseFloat(result.adjustedAmount),
      twdAmount: parseFloat(result.twdAmount),
      adjustedTwdAmount: parseFloat(result.adjustedTwdAmount),
      isLocked: result.isLocked === 1,
      isDeleted: result.isDeleted === 1,
    })
  }
}
