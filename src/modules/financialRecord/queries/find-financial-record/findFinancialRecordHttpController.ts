import { Controller, Get, HttpStatus, Param } from '@nestjs/common'
import { QueryBus } from '@nestjs/cqrs'
import { ApiOperation, ApiResponse } from '@nestjs/swagger'
import { routesV1 } from '@src/config/appRoutes'
import { FinancialRecordDetailResponseDto } from '../../dtos/financialRecordDetailResponseDto'
import { FindFinancialRecordQuery } from './findFinancialRecordQueryHandler'
import { FindFinancialRecordRequestDto } from './findFinancialRecordRequestDto'

@Controller(routesV1.version)
export class FindFinancialRecordHttpController {
  constructor(private readonly queryBus: QueryBus) {}

  @Get(routesV1.financialRecord.getOne)
  @ApiOperation({ summary: 'Find Financial record by id' })
  @ApiResponse({
    status: HttpStatus.OK,
    schema: {
      type: 'object',
      properties: {
        data: {
          $ref: '#/components/schemas/FinancialRecordDetailResponseDto',
        },
      },
    },
  })
  async findFinancialRecord(
    @Param() params: FindFinancialRecordRequestDto,
  ): Promise<{ data: FinancialRecordDetailResponseDto }> {
    const query = new FindFinancialRecordQuery({
      id: params.id,
    })
    const financialRecord: FinancialRecordDetailResponseDto | null =
      await this.queryBus.execute(query)

    if (!financialRecord) {
      throw new Error('Financial record not found')
    }

    return { data: financialRecord }
  }
}
