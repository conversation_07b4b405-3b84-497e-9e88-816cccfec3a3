import { QueryBase } from '@libs/ddd/query.base'
import { Nullable } from '@libs/types'
import { Inject } from '@nestjs/common'
import { IQuery<PERSON><PERSON><PERSON>, QueryHandler } from '@nestjs/cqrs'
import { TypeOrmFinancialRecordRepositoryQueryAdapter } from '../../database/financialRecordQueryRepository'
import { FinancialRecordDetailResponseDto } from '../../dtos/financialRecordDetailResponseDto'
import { FINANCIAL_RECORD_QUERY_REPOSITORY } from '../../financialRecordDiTokens'

export class FindFinancialRecordQuery extends QueryBase {
  readonly id: string

  constructor(props: FindFinancialRecordQuery) {
    super()
    this.id = props.id
  }
}

@QueryHandler(FindFinancialRecordQuery)
export class FindFinancialRecordQueryHandler
  implements
    IQueryHandler<
      FindFinancialRecordQuery,
      Nullable<FinancialRecordDetailResponseDto>
    >
{
  constructor(
    @Inject(FINANCIAL_RECORD_QUERY_REPOSITORY)
    private readonly repository: TypeOrmFinancialRecordRepositoryQueryAdapter,
  ) {}

  /**
   * In read model we don't need to execute
   * any business logic, so we can bypass
   * domain and repository layers completely
   * and execute query directly
   */
  async execute(
    query: FindFinancialRecordQuery,
  ): Promise<Nullable<FinancialRecordDetailResponseDto>> {
    const entity = await this.repository.findOneByIdWithDetails(query.id)
    return entity
  }
}
