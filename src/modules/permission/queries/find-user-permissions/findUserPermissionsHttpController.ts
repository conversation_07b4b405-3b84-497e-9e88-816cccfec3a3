import { Controller, Get, Query, Req } from '@nestjs/common'
import { QueryBus } from '@nestjs/cqrs'
import { routesV1 } from '@src/config/appRoutes'

import { UserPermissionResponseDto } from '../../dtos/userPermissionResponseDto'
import { FindUserPermissionsQuery } from '../find-user-permissions/findUserPermissionsQuery'

@Controller(routesV1.version)
export class FindUserPermissionController {
  constructor(private readonly queryBus: QueryBus) {}

  @Get(routesV1.permission.getUserPermissions)
  async getUserPermissions(
    @Req() request: any,
    @Query('act') act?: string,
  ): Promise<UserPermissionResponseDto> {
    const userId = (request as any).userId as string // 👈 middleware 注入的欄位
    return await this.queryBus.execute(
      new FindUserPermissionsQuery({ userId, act }),
    )
  }
}
