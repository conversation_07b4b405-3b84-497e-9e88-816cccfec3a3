import { TypeOrmApplicationFormRepositoryQueryAdapter } from '@modules/applicationForm/database/applicationFormQueryRepository'
import { TypeOrmApplicationFormEntity } from '@modules/applicationForm/database/typeorm/typeOrmApplicationFormEntity'
import { ApplicationFormsQueryHandler } from '@modules/applicationForm/queries/find-application-forms/findApplicationFormsQueryHandler'

describe('ApplicationFormsQueryHandler', () => {
  let handler: ApplicationFormsQueryHandler
  let mockRepository: jest.Mocked<TypeOrmApplicationFormRepositoryQueryAdapter>

  beforeEach(() => {
    mockRepository = {
      findAll: jest.fn(),
    } as any

    handler = new ApplicationFormsQueryHandler(mockRepository)
  })

  it('should return all application forms', async () => {
    mockRepository.findAll.mockResolvedValue([
      {
        id: 1,
        name: 'Application Form A',
        is_enable: true,
      } as TypeOrmApplicationFormEntity,
    ])

    const result = await handler.execute()

    expect(mockRepository.findAll).toHaveBeenCalled()
    expect(result).not.toBeNull()
    expect(result.length).toBe(1)
    expect(result[0].name).toBe('Application Form A')
  })

  it('should return an empty array if no application forms found', async () => {
    mockRepository.findAll.mockResolvedValue([])

    const result = await handler.execute()

    expect(mockRepository.findAll).toHaveBeenCalled()
    expect(result).toEqual([])
  })
})
