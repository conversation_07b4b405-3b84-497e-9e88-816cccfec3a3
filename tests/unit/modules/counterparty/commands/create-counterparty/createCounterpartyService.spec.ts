import { CounterpartyType } from '@libs/enums/counterpartyEnums'
import { CreateCounterpartyCommand } from '@modules/counterparty/commands/create-counterparty/createCounterpartyCommand'
import { CreateCounterpartyService } from '@modules/counterparty/commands/create-counterparty/createCounterpartyService'
import { COUNTERPARTY_REPOSITORY } from '@modules/counterparty/counterpartyDiTokens'
import { TypeOrmCounterpartyRepositoryAdapter } from '@modules/counterparty/database/counterpartyRepository'
import { CounterpartyEntity } from '@modules/counterparty/domain/counterpartyEntity'
import { Test, TestingModule } from '@nestjs/testing'

jest.mock('@modules/counterparty/database/counterpartyRepository')

describe('CreateCounterpartyService', () => {
  let service: CreateCounterpartyService
  let mockRepository: jest.Mocked<TypeOrmCounterpartyRepositoryAdapter>

  beforeEach(async () => {
    mockRepository = {
      insert: jest.fn(),
      save: jest.fn(),
      isCounterpartyDuplicate: jest.fn(),
      transaction: jest.fn((callback) => callback({})),
    } as any

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CreateCounterpartyService,
        {
          provide: COUNTERPARTY_REPOSITORY,
          useValue: mockRepository,
        },
      ],
    }).compile()

    service = module.get<CreateCounterpartyService>(CreateCounterpartyService)
  })

  it('should create a new counterparty and update sourceId if sourceTable is financial_counterparties', async () => {
    const command = new CreateCounterpartyCommand({
      type: CounterpartyType.DOMESTIC_COMPANY,
      name: 'Test Counterparty',
      identificationNumber: '*********',
      registeredAddress: 'Test Address',
      sourceTable: 'financial_counterparties',
      sourceId: undefined,
    })

    const mockCounterparty = CounterpartyEntity.create({
      type: command.type,
      name: command.name,
      identificationNumber: command.identificationNumber,
      address: command.registeredAddress,
      sourceTable: command.sourceTable || '',
      sourceId: command.sourceId,
    })

    mockCounterparty.updateId('1')
    mockCounterparty.updateSourceId(1)

    mockRepository.insert.mockResolvedValue(1)
    mockRepository.save.mockResolvedValue(1)

    const result = await service.execute(command)

    expect(mockRepository.insert).toHaveBeenCalledWith(
      expect.objectContaining({
        props: expect.objectContaining({
          type: command.type,
          name: command.name,
          identityNumber: command.identificationNumber, // 修正屬性名稱以匹配 CounterpartyEntity
          address: command.registeredAddress,
          sourceTable: command.sourceTable,
          sourceId: 1,
          isEnable: true,
        }),
        _id: '1',
        _createdAt: expect.any(Date),
        _updatedAt: expect.any(Date),
      }),
      expect.any(Object),
    )
    expect(mockCounterparty.sourceId).toBe(1)
    expect(mockRepository.save).toHaveBeenCalledWith(
      expect.objectContaining({
        props: expect.objectContaining({
          type: command.type,
          name: command.name,
          identityNumber: command.identificationNumber,
          address: command.registeredAddress,
          sourceTable: command.sourceTable,
          sourceId: 1,
          isEnable: true,
        }),
        _id: '1',
        _createdAt: expect.any(Date), // 忽略具體時間戳
        _updatedAt: expect.any(Date), // 忽略具體時間戳
      }),
      expect.any(Object),
    )
    expect(result).toBe('1')
  })

  it('should throw an error if counterparty is duplicate', async () => {
    const command = new CreateCounterpartyCommand({
      type: CounterpartyType.DOMESTIC_COMPANY,
      name: 'Duplicate Counterparty',
      identificationNumber: '*********',
      registeredAddress: 'Duplicate Address',
      sourceTable: 'financial_counterparties',
      sourceId: undefined,
    })

    mockRepository.isCounterpartyDuplicate.mockResolvedValue(true)

    await expect(service.execute(command)).rejects.toThrow(
      "交易對象的來源 'financial_counterparties'、類型 'DOMESTIC_COMPANY'、名稱 'Duplicate Counterparty' 及統編/身分證字號 '*********' 已經存在",
    )
    expect(mockRepository.isCounterpartyDuplicate).toHaveBeenCalledWith(
      command.type,
      command.name,
      command.sourceTable,
      command.identificationNumber,
    )
  })
})
