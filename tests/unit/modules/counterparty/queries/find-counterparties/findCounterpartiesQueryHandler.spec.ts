import { CounterpartyType } from '@libs/enums/counterpartyEnums'
import { COUNTERPARTY_QUERY_REPOSITORY } from '@modules/counterparty/counterpartyDiTokens'
import { TypeOrmCounterpartyRepositoryQueryAdapter } from '@modules/counterparty/database/counterpartyQueryRepository'
import { TypeOrmCounterpartyEntity } from '@modules/counterparty/database/typeorm/typeOrmCounterpartyEntity'
import { CounterpartiesQueryHandler } from '@modules/counterparty/queries/find-counterparties/findCounterpartiesQueryHandler'
import { CounterpartiesQuery } from '@modules/counterparty/queries/find-counterparties/findCounterpartiesQueryHandler'
import { Test, TestingModule } from '@nestjs/testing'

describe('CounterpartiesQueryHandler', () => {
  let handler: CounterpartiesQueryHandler
  let mockRepository: jest.Mocked<TypeOrmCounterpartyRepositoryQueryAdapter>

  beforeEach(async () => {
    mockRepository = {
      findAll: jest.fn(),
    } as any

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CounterpartiesQueryHandler,
        {
          provide: COUNTERPARTY_QUERY_REPOSITORY,
          useValue: mockRepository,
        },
      ],
    }).compile()

    handler = module.get<CounterpartiesQueryHandler>(CounterpartiesQueryHandler)
  })

  it('should return a list of counterparties', async () => {
    const query = new CounterpartiesQuery(CounterpartyType.DOMESTIC_COMPANY)
    const mockEntities: TypeOrmCounterpartyEntity[] = [
      {
        id: 1,
        type: CounterpartyType.DOMESTIC_COMPANY,
        name: 'Test Counterparty',
        identificationNumber: '*********',
        registeredAddress: 'Test Address',
        is_enable: true,
        source_id: 1, // 新增 source_id 屬性
        source_table: 'test_table', // 新增 source_table 屬性
        created_at: new Date('2025-06-01T00:00:00Z'),
        updated_at: new Date('2025-06-01T00:00:00Z'),
      } as TypeOrmCounterpartyEntity,
    ]

    mockRepository.findAll.mockResolvedValue(mockEntities)

    const result = await handler.execute(query)

    expect(mockRepository.findAll).toHaveBeenCalledWith(
      CounterpartyType.DOMESTIC_COMPANY,
    )
    expect(result).toEqual(mockEntities)
  })

  it('should return an empty list if no counterparties found', async () => {
    const query = new CounterpartiesQuery(CounterpartyType.DOMESTIC_COMPANY)
    mockRepository.findAll.mockResolvedValue([])

    const result = await handler.execute(query)

    expect(mockRepository.findAll).toHaveBeenCalledWith(
      CounterpartyType.DOMESTIC_COMPANY,
    )
    expect(result).toEqual([])
  })
})
