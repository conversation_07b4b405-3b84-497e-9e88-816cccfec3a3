import { TypeOrmFinancialRecordRepositoryQueryAdapter } from '@modules/financialRecord/database/financialRecordQueryRepository'
import { FinancialRecordDetailResponseDto } from '@modules/financialRecord/dtos/financialRecordDetailResponseDto'
import {
  FindFinancialRecordQuery,
  FindFinancialRecordQueryHandler,
} from '@modules/financialRecord/queries/find-financial-record/findFinancialRecordQueryHandler'

describe('FindFinancialRecordQueryHandler', () => {
  let handler: FindFinancialRecordQueryHandler
  let mockRepo: jest.Mocked<TypeOrmFinancialRecordRepositoryQueryAdapter>

  beforeEach(() => {
    mockRepo = {
      findOneById: jest.fn(),
      findOneByIdWithDetails: jest.fn(),
    } as any

    handler = new FindFinancialRecordQueryHandler(mockRepo)
  })

  it('should return financial record when found', async () => {
    const fakeEntity = new FinancialRecordDetailResponseDto({
      id: 1,
      transactionType: 'EXPENSE' as any,
      subsidiaryId: 1,
      counterpartyId: 1,
      subAccountId: 1,
      mainAccountId: 1,
      applicationFormId: 1,
      date: '2025-01-01',
      currencyCode: 'USD',
      exchangeRate: 1.0,
      adjustedExchangeRate: 1.0,
      amount: 100,
      adjustedAmount: 100,
      twdAmount: 100,
      adjustedTwdAmount: 100,
      accrualVoucherNumber: null,
      actualVoucherNumber: null,
      invoiceNumber: null,
      uniformInvoiceNumber: null,
      invoiceDate: null,
      note: null,
      isLocked: false,
      isDeleted: false,
      creatorId: 1,
      subsidiaryName: 'Test Subsidiary',
      counterpartyName: 'Test Counterparty',
      identificationNumber: '123456',
      applicationFormName: 'Test Form',
      counterpartyEntityType: 'DOMESTIC_COMPANY' as any,
      registeredAddress: 'Test Address',
      mainAccountName: 'Test Main Account',
      subAccountName: 'Test Sub Account',
      creatorName: 'Test Creator',
    })
    mockRepo.findOneByIdWithDetails.mockResolvedValue(fakeEntity)

    const query = new FindFinancialRecordQuery({ id: '1' })
    const result = await handler.execute(query)

    expect(mockRepo.findOneByIdWithDetails).toHaveBeenCalledWith('1')
    expect(result).toBe(fakeEntity)
  })

  it('should return null when record not found', async () => {
    mockRepo.findOneByIdWithDetails.mockResolvedValue(null)

    const query = new FindFinancialRecordQuery({ id: '999' })
    const result = await handler.execute(query)

    expect(mockRepo.findOneByIdWithDetails).toHaveBeenCalledWith('999')
    expect(result).toBeNull()
  })
})
