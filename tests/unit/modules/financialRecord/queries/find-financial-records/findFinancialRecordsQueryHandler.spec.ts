import { Paginated } from '@libs/ddd'
// tests/unit/modules/financialRecord/queries/find-financial-records/findFinancialRecordsQueryHandler.spec.ts
import {
  FindFinancialRecordsQuery,
  FindFinancialRecordsQueryHandler,
} from '@modules/financialRecord/queries/find-financial-records/findFinancialRecordsQueryHandler'
import { DataSource, SelectQueryBuilder } from 'typeorm'

// 測試資料工廠函數 - 完全對應 FinancialRecordRawData 介面和實際查詢結果
const createMockFinancialRecordRawData = (overrides: Partial<any> = {}) => ({
  // 來自 financial_records 表格的欄位
  id: 1,
  subsidiaryId: 1, // fr.subsidiary_id -> subsidiary.id
  transactionType: 'INCOME', // fr.transaction_type
  counterpartyId: 2, // fr.counterparty_id
  subAccountId: 4, // fr.sub_account_id
  date: '2024-06-01', // DATE_FORMAT(fr.date, '%Y-%m-%d')
  currencyCode: 'TWD', // fr.currency_code
  exchangeRate: '1.00', // fr.exchange_rate (字串格式)
  adjustedExchangeRate: '1.00', // fr.adjusted_exchange_rate (字串格式)
  amount: '1000.00', // fr.amount (字串格式)
  adjustedAmount: '1000.00', // fr.adjusted_amount (字串格式)
  twdAmount: '1000.00', // fr.TWD_amount (字串格式)
  adjustedTwdAmount: '1000.00', // fr.adjusted_TWD_amount (字串格式)
  accrualVoucherNumber: 'ACC001', // fr.accrual_voucher_number
  actualVoucherNumber: 'ACT001', // fr.actual_voucher_number
  invoiceNumber: 'INV001', // fr.invoice_number
  uniformInvoiceNumber: 'UNI001', // fr.uniform_invoice_number
  invoiceDate: '2024-06-02', // DATE_FORMAT(fr.invoice_date, '%Y-%m-%d')
  note: '測試備註', // fr.note
  isLocked: 0, // fr.is_locked (數字格式)
  isDeleted: 0, // fr.is_deleted (數字格式)
  creatorId: 5, // fr.creator_id
  created_at: new Date('2024-06-01T10:00:00Z'), // fr.created_at
  updated_at: new Date('2024-06-01T10:00:00Z'), // fr.updated_at

  // 來自 JOIN 其他表格的欄位
  subsidiaryName: '測試子公司', // subsidiary.name
  counterpartyName: '測試客戶', // counterparty.name
  identificationNumber: 'ABC123456', // counterparty.identity_number
  counterpartyEntityType: 'PERSON', // counterparty.type
  registeredAddress: '台北市信義區信義路五段7號', // counterparty.address
  mainAccountId: 3, // main_account.id
  mainAccountName: '營業收入', // main_account.name
  subAccountName: '銷售收入', // sub_account.name
  applicationFormId: 1, // sub_account.application_form_id
  applicationFormName: '測試表單', // application_form.name
  creatorName: '系統 管理者', // CONCAT(creator.firstname, ' ', creator.lastname)

  ...overrides,
})

describe('FindFinancialRecordsQueryHandler', () => {
  let handler: FindFinancialRecordsQueryHandler
  let mockQueryBuilder: jest.Mocked<SelectQueryBuilder<any>>
  let mockDataSource: Partial<DataSource>

  beforeEach(() => {
    mockQueryBuilder = {
      select: jest.fn().mockReturnThis(),
      addSelect: jest.fn().mockReturnThis(),
      from: jest.fn().mockReturnThis(),
      leftJoin: jest.fn().mockReturnThis(),
      where: jest.fn().mockReturnThis(),
      andWhere: jest.fn().mockReturnThis(),
      orderBy: jest.fn().mockReturnThis(),
      offset: jest.fn().mockReturnThis(),
      limit: jest.fn().mockReturnThis(),
      getRawOne: jest.fn(),
      getRawMany: jest.fn(),
      clone: jest.fn().mockReturnThis(),
    } as any

    mockDataSource = {
      createQueryBuilder: jest.fn().mockReturnValue(mockQueryBuilder),
    }

    handler = new FindFinancialRecordsQueryHandler(mockDataSource as DataSource)
  })

  it('should return paginated financial records with correct pagination info', async () => {
    const query = new FindFinancialRecordsQuery({
      startDate: '2024-01-01',
      endDate: '2024-12-31',
      currentPage: 1,
      itemCounts: 10,
    })

    mockQueryBuilder.getRawOne.mockResolvedValue({ count: '25' }) // 總共 25 筆資料
    mockQueryBuilder.getRawMany.mockResolvedValue([
      createMockFinancialRecordRawData({ id: 1 }),
    ])

    const result = await handler.execute(query)

    expect(result).toBeInstanceOf(Paginated)
    expect(mockQueryBuilder.getRawOne).toHaveBeenCalled()
    expect(mockQueryBuilder.getRawMany).toHaveBeenCalled()
    expect(result).not.toBeNull()

    // 驗證分頁資訊
    expect(result?.totalCounts).toBe(25) // 總筆數
    expect(result?.pageCounts).toBe(3) // Math.ceil(25/10) = 3 頁
    expect(result?.currentPage).toBe(1) // 當前頁數
    expect(result?.itemCounts).toBe(1) // 當前頁面實際筆數
    expect(result?.data.length).toBe(1) // 資料陣列長度
  })

  it('should calculate pagination correctly for edge cases', async () => {
    const query = new FindFinancialRecordsQuery({
      startDate: '2024-01-01',
      endDate: '2024-12-31',
      currentPage: 2,
      itemCounts: 5,
    })

    mockQueryBuilder.getRawOne.mockResolvedValue({ count: '12' }) // 總共 12 筆資料
    mockQueryBuilder.getRawMany.mockResolvedValue([
      // 模擬第二頁的 3 筆資料
      createMockFinancialRecordRawData({
        id: 6,
        amount: '2000.00',
        adjustedAmount: '2000.00',
      }),
      createMockFinancialRecordRawData({
        id: 7,
        amount: '3000.00',
        adjustedAmount: '3000.00',
      }),
      createMockFinancialRecordRawData({
        id: 8,
        amount: '4000.00',
        adjustedAmount: '4000.00',
      }),
    ])

    const result = await handler.execute(query)

    expect(result?.totalCounts).toBe(12) // 總筆數
    expect(result?.pageCounts).toBe(3) // Math.ceil(12/5) = 3 頁
    expect(result?.currentPage).toBe(2) // 當前頁數
    expect(result?.itemCounts).toBe(3) // 當前頁面實際筆數（第二頁有 3 筆）
  })

  it('should handle empty results correctly', async () => {
    const query = new FindFinancialRecordsQuery({
      startDate: '2024-01-01',
      endDate: '2024-12-31',
      currentPage: 1,
      itemCounts: 10,
    })

    mockQueryBuilder.getRawOne.mockResolvedValue({ count: '0' })
    mockQueryBuilder.getRawMany.mockResolvedValue([])

    const result = await handler.execute(query)

    expect(result?.totalCounts).toBe(0)
    expect(result?.pageCounts).toBe(0) // Math.ceil(0/10) = 0
    expect(result?.currentPage).toBe(1)
    expect(result?.itemCounts).toBe(0) // 沒有資料
    expect(result?.data.length).toBe(0)
  })
})
