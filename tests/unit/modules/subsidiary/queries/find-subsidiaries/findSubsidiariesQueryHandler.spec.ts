import { TypeOrmSubsidiaryRepositoryQueryAdapter } from '@modules/subsidiary/database/subsidiaryQueryRepository'
import { TypeOrmSubsidiaryEntity } from '@modules/subsidiary/database/typeorm/typeOrmSubsidiaryEntity'
import { SubsidiariesQueryHandler } from '@modules/subsidiary/queries/find-subsidiaries/findSubsidiariesQueryHandler'
import { SUBSIDIARY_QUERY_REPOSITORY } from '@modules/subsidiary/subsidiaryDiTokens'
import { Test, TestingModule } from '@nestjs/testing'

describe('SubsidiariesQueryHandler', () => {
  let handler: SubsidiariesQueryHandler
  let mockRepository: jest.Mocked<TypeOrmSubsidiaryRepositoryQueryAdapter>

  beforeEach(async () => {
    mockRepository = {
      findAll: jest.fn(),
    } as any

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        SubsidiariesQueryHandler,
        {
          provide: SUBSIDIARY_QUERY_REPOSITORY,
          useValue: mockRepository,
        },
      ],
    }).compile()

    handler = module.get<SubsidiariesQueryHandler>(SubsidiariesQueryHandler)
  })

  it('should return a list of subsidiaries', async () => {
    const mockEntities: TypeOrmSubsidiaryEntity[] = [
      {
        id: 1,
        name: 'Test Subsidiary',
        is_enable: true,
      } as TypeOrmSubsidiaryEntity,
    ]

    mockRepository.findAll.mockResolvedValue(mockEntities)

    const result = await handler.execute()

    expect(mockRepository.findAll).toHaveBeenCalled()
    expect(result).toEqual(mockEntities)
  })

  it('should return an empty list if no subsidiaries found', async () => {
    mockRepository.findAll.mockResolvedValue([])

    const result = await handler.execute()

    expect(mockRepository.findAll).toHaveBeenCalled()
    expect(result).toEqual([])
  })
})
