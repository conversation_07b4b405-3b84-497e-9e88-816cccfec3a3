{"compilerOptions": {"strict": true, "module": "commonjs", "declaration": true, "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "strictPropertyInitialization": false, "target": "es2024", "sourceMap": true, "outDir": "./dist", "baseUrl": "./", "incremental": true, "skipLibCheck": true, "strictNullChecks": true, "noImplicitAny": false, "strictBindCallApply": false, "forceConsistentCasingInFileNames": false, "noFallthroughCasesInSwitch": false, "paths": {"@src/*": ["src/*"], "@modules/*": ["src/modules/*"], "@config/*": ["src/configs/*"], "@libs/*": ["src/libs/*"], "@tests/*": ["test/*"]}}}